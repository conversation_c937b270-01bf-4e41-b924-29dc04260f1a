package com.amobear.themepack.presentation.rewards

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.amobear.themepack.R
import com.amobear.themepack.presentation.home.components.CoinIndicator
import com.amobear.themepack.presentation.rewards.components.CoinPackageCard
import com.amobear.themepack.presentation.rewards.components.DailyCheckInCard
import com.amobear.themepack.presentation.rewards.components.RewardTaskCard

// 1. Route (Stateful)
@Composable
fun RewardsRoute(
    onNavigateBack: () -> Unit,
    viewModel: RewardsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    RewardsScreen(
        state = state,
        onIntent = viewModel::processIntent,
        onNavigateBack = onNavigateBack
    )
}

// 2. Screen (Stateless)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsScreen(
    state: RewardsState,
    onIntent: (RewardsIntent) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = Color(0xFFF6F2F2)
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.rewards_title),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_back), // Using existing icon, should be back arrow
                            contentDescription = "Back",
                            tint = Color.Black
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor
                )
            )
        },
        containerColor = backgroundColor
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // Coin Balance Header
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.coin_balance),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Black
                    )
                    
                    CoinIndicator(
                        coins = uiState.coinBalance,
                        onAddCoinsClick = {}
                    )
                }
            }
            
            item {
                // Daily Check-in Card
                DailyCheckInCard(
                    dailyRewards = uiState.dailyRewards,
                    currentStreak = uiState.currentStreak,
                    onCheckInClick = viewModel::onCheckIn,
                    showInfoDialog = uiState.showCheckInInfoDialog,
                    onShowInfoDialog = viewModel::onShowCheckInInfoDialog,
                    onDismissInfoDialog = viewModel::onDismissCheckInInfoDialog
                )
            }
            
            item {
                // Remind Me Tomorrow Toggle
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(R.string.remind_me_tomorrow),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black
                        )
                        
                        Switch(
                            checked = uiState.remindMeTomorrow,
                            onCheckedChange = viewModel::onRemindMeTomorrowChanged,
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color.White,
                                checkedTrackColor = Color(0xFF4CAF50),
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color.Gray
                            )
                        )
                    }
                }
            }
            
            items(uiState.rewardTasks) { task ->
                RewardTaskCard(
                    task = task,
                    onActionClick = { viewModel.onTaskAction(task.id) }
                )
            }
            
            item {
                // Coin Packages Section
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(vertical = 8.dp)
                ) {
                    items(uiState.coinPackages) { coinPackage ->
                        CoinPackageCard(
                            coinPackage = coinPackage,
                            onPurchaseClick = { viewModel.onPurchaseCoinPackage(coinPackage.id) }
                        )
                    }
                }
            }
            
            item {
                // Bottom spacing
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Preview(showBackground = true,heightDp = 10000)
@Composable
fun RewardsScreenPreview() {
    RewardsScreen()
} 